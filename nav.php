<?php
// Navigation component
?>
<header>
    <div class="logo">MARKETX</div>
    <nav>
        <a href="index.php">HOME</a>
        <a href="about.php">ABOUT</a>
        <a href="contact.php">CONTACT</a>
        <?php if (isset($_SESSION['user'])): ?>
            <?php if ($_SESSION['user']['role'] === 'buyer'): ?>
                <a href="cart.php">🛒 CART</a>
                <a href="message.php">MESSAGES</a>
            <?php elseif ($_SESSION['user']['role'] === 'seller'): ?>
                <a href="cart.php">🛒 CART</a>
                <a href="message.php">MESSAGES</a>
                <a href="sell.php">SELL</a>
                <a href="my_products.php">MY PRODUCTS</a>
            <?php elseif ($_SESSION['user']['role'] === 'admin'): ?>
                <a href="cart.php">🛒 CART</a>
                <a href="message.php">MESSAGES</a>
                <a href="admin/">ADMIN PANEL</a>
            <?php endif; ?>
            <a href="logout.php" style="background-color: #ffd700; color: black; padding: 5px 10px; border-radius: 3px; margin-left: 15px;">LOGOUT</a>
        <?php else: ?>
            <a href="login.php">LOGIN</a>
            <a href="register.php" style="background-color: #ffd700; color: black; padding: 5px 10px; border-radius: 3px;">REGISTER</a>
        <?php endif; ?>
    </nav>
</header>

<script>
// Simple JavaScript to enhance user experience
document.addEventListener('DOMContentLoaded', function() {
    // Add loading state to forms
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.textContent = 'Processing...';
                submitBtn.disabled = true;
            }
        });
    });
});
</script>
