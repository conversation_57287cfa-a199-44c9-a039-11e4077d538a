<?php
include 'config.php';
session_start();
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'seller') {
    die("Access denied.");
}
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = $_POST['title'];
    $desc = $_POST['description'];
    $price = $_POST['price'];
    $image = $_POST['image'];
    $category_id = $_POST['category_id'];
    $user_id = $_SESSION['user']['id'];

    $stmt = $conn->prepare("INSERT INTO products (user_id, category_id, title, description, price, image) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("iissds", $user_id, $category_id, $title, $desc, $price, $image);
    if ($stmt->execute()) {
        echo "<div style='color: green; margin: 10px 0; padding: 10px; border: 1px solid green; background-color: #e6ffe6;'>";
        echo "Product listed successfully! <a href='index.php'>View all products</a>";
        echo "</div>";
    } else {
        echo "<div style='color: red; margin: 10px 0; padding: 10px; border: 1px solid red; background-color: #ffe6e6;'>";
        echo "Error listing product. Please try again.";
        echo "</div>";
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Sell Product</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
<?php include 'nav.php'; ?>

<main>
    <h2 class="center-title">Sell on <span class="highlight">MarketX</span></h2>
    <div class="form-container">
        <form method="post">
            <label>Product Name</label>
            <input type="text" name="title" placeholder="Enter product name" required>

            <label>Category</label>
            <select name="category_id" required>
                <option value="">Select Category</option>
                <?php
                $categories = $conn->query("SELECT * FROM categories");
                if ($categories) {
                    while ($cat = $categories->fetch_assoc()) {
                        echo "<option value='{$cat['id']}'>{$cat['name']}</option>";
                    }
                }
                ?>
            </select>

            <label>Price (R)</label>
            <input type="number" name="price" placeholder="Enter product price" step="0.01" required>

            <label>Product Description</label>
            <textarea name="description" placeholder="Describe your product" rows="4" required></textarea>

            <label>Product Image URL</label>
            <input type="text" name="image" placeholder="Enter product image URL" required>

            <button type="submit" class="submit-btn">Submit Listing</button>
        </form>
    </div>
</main>

<?php include 'footer.php'; ?>
</body>
</html>
