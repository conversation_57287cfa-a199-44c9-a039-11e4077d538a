<?php
include 'config.php';
session_start();
$error_message = "";
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'];
    $password = $_POST['password'];

    $stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $user = $stmt->get_result()->fetch_assoc();

    if ($user && password_verify($password, $user['password'])) {
        $_SESSION['user'] = $user;
        header("Location: index.php");
        exit();
    } else {
        $error_message = "Invalid email or password. Please try again.";
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Login</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
<?php include 'nav.php'; ?>

<main>
    <h2 class="center-title">Login to <span class="highlight">MarketX</span></h2>

    <?php if (isset($_GET['registered']) && $_GET['registered'] == 'success'): ?>
        <div class="success-message">
            🎉 Registration successful! Please login with your new account.
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="error-message">
            <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>

    <div class="form-container">
        <form method="post">
            <label>Email</label>
            <input type="email" name="email" placeholder="Enter your email" required>

            <label>Password</label>
            <input type="password" name="password" placeholder="Enter your password" required>

            <button type="submit" class="submit-btn">Login</button>
        </form>

        <p style="text-align: center; margin-top: 20px;">
            Don't have an account? <a href="register.php" style="color: #ff6f61; font-weight: bold;">Register here</a>
        </p>
    </div>
</main>

<?php include 'footer.php'; ?>
</body>
</html>
