<?php
session_start();
include 'config.php';
if (!isset($_SESSION['user'])) {
    die("You must be logged in to checkout.");
}

$user_id = $_SESSION['user']['id'];

// Get cart items with prepared statement
$stmt = $conn->prepare("SELECT cart.*, products.price
                        FROM cart
                        JOIN products ON cart.product_id = products.id
                        WHERE cart.user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$res = $stmt->get_result();

$total = 0;
$items = [];

while ($row = $res->fetch_assoc()) {
    $total += $row['price'] * $row['quantity'];
    $items[] = $row;
}

// Insert order with prepared statement
$order_stmt = $conn->prepare("INSERT INTO orders (user_id, total) VALUES (?, ?)");
$order_stmt->bind_param("id", $user_id, $total);
$order_stmt->execute();
$order_id = $conn->insert_id;

// Insert order items with prepared statement
$item_stmt = $conn->prepare("INSERT INTO order_items (order_id, product_id, quantity, price) VALUES (?, ?, ?, ?)");
foreach ($items as $item) {
    $item_stmt->bind_param("iiid", $order_id, $item['product_id'], $item['quantity'], $item['price']);
    $item_stmt->execute();
}

// Clear cart with prepared statement
$clear_stmt = $conn->prepare("DELETE FROM cart WHERE user_id = ?");
$clear_stmt->bind_param("i", $user_id);
$clear_stmt->execute();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Order Confirmation | MarketX</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
<?php include 'nav.php'; ?>

<main>
    <div style="max-width: 600px; margin: 50px auto; padding: 0 20px;">
        <div class="form-container" style="text-align: center;">
            <div style="margin-bottom: 30px;">
                <div style="font-size: 48px; margin-bottom: 20px;">✅</div>
                <h2 style="color: #28a745; margin-bottom: 15px;">Order Placed Successfully!</h2>
                <p style="color: #666; font-size: 16px; margin-bottom: 30px;">
                    Thank you for your purchase! Your order has been confirmed and is being processed.
                </p>
            </div>

            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
                <h3 style="color: #ff6f61; margin-bottom: 15px;">Order Details</h3>
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <span style="color: #666;">Order Number:</span>
                    <span style="font-weight: bold;">#<?php echo $order_id; ?></span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <span style="color: #666;">Total Amount:</span>
                    <span style="font-weight: bold; color: #ff6f61; font-size: 18px;">R<?php echo number_format($total, 2); ?></span>
                </div>
                <div style="display: flex; justify-content: space-between;">
                    <span style="color: #666;">Order Date:</span>
                    <span style="font-weight: bold;"><?php echo date('M j, Y g:i A'); ?></span>
                </div>
            </div>

            <div style="margin-bottom: 30px;">
                <h4 style="color: #ff6f61; margin-bottom: 15px;">What's Next?</h4>
                <ul style="text-align: left; color: #666; line-height: 1.6;">
                    <li>You'll receive an email confirmation shortly</li>
                    <li>Sellers will be notified of your purchase</li>
                    <li>You can track your orders in your account</li>
                    <li>Contact sellers directly for any questions</li>
                </ul>
            </div>

            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                <a href="index.php" class="btn-yellow" style="padding: 12px 25px;">Continue Shopping</a>
                <?php if (isset($_SESSION['user'])): ?>
                    <a href="message.php" class="submit-btn" style="padding: 12px 25px; text-decoration: none;">View Messages</a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</main>

<?php include 'footer.php'; ?>
</body>
</html>
<?php
?>
