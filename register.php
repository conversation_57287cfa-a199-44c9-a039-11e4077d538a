<?php
include 'config.php';
$error_message = "";
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'];
    $email = $_POST['email'];
    $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
    $role = $_POST['role'];

    // Check if email already exists
    $check_stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
    $check_stmt->bind_param("s", $email);
    $check_stmt->execute();
    $existing_user = $check_stmt->get_result()->fetch_assoc();

    if ($existing_user) {
        $error_message = "Email already registered. Please use a different email.";
    } else {
        $stmt = $conn->prepare("INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, ?)");
        $stmt->bind_param("ssss", $username, $email, $password, $role);

        if ($stmt->execute()) {
            $success_registration = true;
        } else {
            $error_message = "Registration failed. Please try again.";
        }
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Register</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
<?php include 'nav.php'; ?>

<main>
    <h2 class="center-title">Register on <span class="highlight">MarketX</span></h2>

    <?php if (isset($success_registration) && $success_registration): ?>
        <!-- Success Popup -->
        <div id="successPopup" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 1000; display: flex; justify-content: center; align-items: center;">
            <div style="background: white; padding: 40px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); text-align: center; max-width: 400px; width: 90%; animation: slideIn 0.3s ease-out;">
                <div style="font-size: 60px; margin-bottom: 20px;">🎉</div>
                <h2 style="color: #28a745; margin-bottom: 15px;">Registration Successful!</h2>
                <p style="color: #666; margin-bottom: 25px;">Welcome to MarketX! Your account has been created successfully.</p>
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 25px;">
                    <p style="color: #666; margin: 0; font-size: 14px;">Redirecting to login page in <span id="countdown">3</span> seconds...</p>
                </div>
                <a href="login.php?registered=success" class="btn-yellow" style="padding: 12px 25px; text-decoration: none;">Login Now</a>
            </div>
        </div>

        <style>
        @keyframes slideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        </style>

        <script>
        let countdown = 3;
        const countdownElement = document.getElementById('countdown');

        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;

            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = 'login.php?registered=success';
            }
        }, 1000);
        </script>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="error-message">
            <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>

    <div class="form-container">
        <form method="post">
            <label>Username</label>
            <input type="text" name="username" placeholder="Enter your username" required>

            <label>Email</label>
            <input type="email" name="email" placeholder="Enter your email" required>

            <label>Password</label>
            <input type="password" name="password" placeholder="Enter your password" required>

            <label>Account Type</label>
            <select name="role">
                <option value="buyer">Buyer</option>
                <option value="seller">Seller</option>
                <option value="admin">Admin</option>
            </select>

            <button type="submit" class="submit-btn">Register</button>
        </form>

        <p style="text-align: center; margin-top: 20px;">
            Already have an account? <a href="login.php" style="color: #ff6f61; font-weight: bold;">Login here</a>
        </p>
    </div>
</main>

<?php include 'footer.php'; ?>
</body>
</html>
