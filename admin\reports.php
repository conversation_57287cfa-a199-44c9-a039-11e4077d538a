<?php
session_start();
include '../config.php';

if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
    die("Access denied. Admins only.");
}

// Get various statistics
$stats = [];

// User statistics
$stats['total_users'] = $conn->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
$stats['buyers'] = $conn->query("SELECT COUNT(*) as count FROM users WHERE role = 'buyer'")->fetch_assoc()['count'];
$stats['sellers'] = $conn->query("SELECT COUNT(*) as count FROM users WHERE role = 'seller'")->fetch_assoc()['count'];
$stats['admins'] = $conn->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'")->fetch_assoc()['count'];

// Product statistics
$stats['total_products'] = $conn->query("SELECT COUNT(*) as count FROM products")->fetch_assoc()['count'];
$stats['total_categories'] = $conn->query("SELECT COUNT(*) as count FROM categories")->fetch_assoc()['count'];

// Order statistics
$stats['total_orders'] = $conn->query("SELECT COUNT(*) as count FROM orders")->fetch_assoc()['count'];
$total_revenue = $conn->query("SELECT SUM(total) as revenue FROM orders")->fetch_assoc()['revenue'];
$stats['total_revenue'] = $total_revenue ? $total_revenue : 0;

// Message statistics
$stats['total_messages'] = $conn->query("SELECT COUNT(*) as count FROM messages")->fetch_assoc()['count'];

// Top selling products
$top_products = $conn->query("SELECT p.title, u.username as seller, COALESCE(SUM(oi.quantity), 0) as total_sold, COALESCE(SUM(oi.quantity * oi.price), 0) as revenue
                             FROM products p
                             JOIN users u ON p.user_id = u.id
                             LEFT JOIN order_items oi ON p.id = oi.product_id
                             GROUP BY p.id
                             ORDER BY total_sold DESC
                             LIMIT 10");

// Top buyers
$top_buyers = $conn->query("SELECT u.username, COUNT(o.id) as order_count, SUM(o.total) as total_spent
                           FROM orders o 
                           JOIN users u ON o.user_id = u.id 
                           GROUP BY u.id 
                           ORDER BY total_spent DESC 
                           LIMIT 10");

// Top sellers
$top_sellers = $conn->query("SELECT u.username, COUNT(p.id) as product_count, COALESCE(SUM(oi.quantity * oi.price), 0) as revenue
                            FROM users u 
                            LEFT JOIN products p ON u.id = p.user_id 
                            LEFT JOIN order_items oi ON p.id = oi.product_id 
                            WHERE u.role = 'seller' 
                            GROUP BY u.id 
                            ORDER BY revenue DESC 
                            LIMIT 10");

// Category statistics
$category_stats = $conn->query("SELECT c.name, COUNT(p.id) as product_count
                               FROM categories c 
                               LEFT JOIN products p ON c.id = p.category_id 
                               GROUP BY c.id 
                               ORDER BY product_count DESC");
?>
<!DOCTYPE html>
<html>
<head>
    <title>Reports - Admin Panel</title>
    <link rel="stylesheet" href="../style.css">
    <style>
        .admin-nav {
            background-color: #343a40;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .admin-nav a {
            color: white;
            margin-right: 20px;
            padding: 8px 15px;
            background-color: #007bff;
            border-radius: 4px;
            text-decoration: none;
        }
        .admin-nav a:hover {
            background-color: #0056b3;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .report-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
<?php include '../nav.php'; ?>

<div class="admin-nav">
    <a href="index.php">Dashboard</a>
    <a href="users.php">Manage Users</a>
    <a href="products.php">Manage Products</a>
    <a href="categories.php">Manage Categories</a>
    <a href="reports.php">Reports</a>
</div>

<h2>Platform Reports & Statistics</h2>

<!-- Overall Statistics -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-number"><?php echo $stats['total_users']; ?></div>
        <div>Total Users</div>
        <small><?php echo $stats['buyers']; ?> Buyers, <?php echo $stats['sellers']; ?> Sellers, <?php echo $stats['admins']; ?> Admins</small>
    </div>
    <div class="stat-card">
        <div class="stat-number"><?php echo $stats['total_products']; ?></div>
        <div>Total Products</div>
        <small>Across <?php echo $stats['total_categories']; ?> categories</small>
    </div>
    <div class="stat-card">
        <div class="stat-number"><?php echo $stats['total_orders']; ?></div>
        <div>Total Orders</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">R<?php echo number_format($stats['total_revenue'], 2); ?></div>
        <div>Total Revenue</div>
    </div>
    <div class="stat-card">
        <div class="stat-number"><?php echo $stats['total_messages']; ?></div>
        <div>Total Messages</div>
    </div>
</div>

<!-- Top Products -->
<div class="report-section">
    <h3>Top Selling Products</h3>
    <?php if ($top_products->num_rows > 0): ?>
        <table>
            <thead>
                <tr>
                    <th>Product</th>
                    <th>Seller</th>
                    <th>Units Sold</th>
                    <th>Revenue</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($product = $top_products->fetch_assoc()): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($product['title']); ?></td>
                        <td><?php echo htmlspecialchars($product['seller']); ?></td>
                        <td><?php echo $product['total_sold']; ?></td>
                        <td>R<?php echo number_format($product['revenue'], 2); ?></td>
                    </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
    <?php else: ?>
        <p>No sales data available yet.</p>
    <?php endif; ?>
</div>

<!-- Top Buyers -->
<div class="report-section">
    <h3>Top Buyers</h3>
    <?php if ($top_buyers->num_rows > 0): ?>
        <table>
            <thead>
                <tr>
                    <th>Username</th>
                    <th>Orders</th>
                    <th>Total Spent</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($buyer = $top_buyers->fetch_assoc()): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($buyer['username']); ?></td>
                        <td><?php echo $buyer['order_count']; ?></td>
                        <td>R<?php echo number_format($buyer['total_spent'], 2); ?></td>
                    </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
    <?php else: ?>
        <p>No buyer data available yet.</p>
    <?php endif; ?>
</div>

<!-- Top Sellers -->
<div class="report-section">
    <h3>Top Sellers</h3>
    <?php if ($top_sellers->num_rows > 0): ?>
        <table>
            <thead>
                <tr>
                    <th>Username</th>
                    <th>Products Listed</th>
                    <th>Revenue Generated</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($seller = $top_sellers->fetch_assoc()): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($seller['username']); ?></td>
                        <td><?php echo $seller['product_count']; ?></td>
                        <td>R<?php echo number_format($seller['revenue'], 2); ?></td>
                    </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
    <?php else: ?>
        <p>No seller data available yet.</p>
    <?php endif; ?>
</div>

<!-- Category Statistics -->
<div class="report-section">
    <h3>Products by Category</h3>
    <table>
        <thead>
            <tr>
                <th>Category</th>
                <th>Product Count</th>
            </tr>
        </thead>
        <tbody>
            <?php while ($category = $category_stats->fetch_assoc()): ?>
                <tr>
                    <td><?php echo htmlspecialchars($category['name']); ?></td>
                    <td><?php echo $category['product_count']; ?></td>
                </tr>
            <?php endwhile; ?>
        </tbody>
    </table>
</div>

<?php include '../footer.php'; ?>
</body>
</html>
