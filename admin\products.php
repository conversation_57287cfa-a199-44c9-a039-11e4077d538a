<?php
session_start();
include '../config.php';

if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
    die("Access denied. Admins only.");
}

$success_message = "";
$error_message = "";

// Handle product deletion
if (isset($_POST['delete_product'])) {
    $product_id = $_POST['product_id'];
    $stmt = $conn->prepare("DELETE FROM products WHERE id = ?");
    $stmt->bind_param("i", $product_id);
    if ($stmt->execute()) {
        $success_message = "Product deleted successfully!";
    } else {
        $error_message = "Failed to delete product.";
    }
}

// Get all products with seller and category info
$products = $conn->query("SELECT p.*, u.username as seller_name, c.name as category_name 
                         FROM products p 
                         JOIN users u ON p.user_id = u.id 
                         LEFT JOIN categories c ON p.category_id = c.id 
                         ORDER BY p.created_at DESC");
?>
<!DOCTYPE html>
<html>
<head>
    <title>Manage Products - Admin Panel</title>
    <link rel="stylesheet" href="../style.css">
    <style>
        .admin-nav {
            background-color: #343a40;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .admin-nav a {
            color: white;
            margin-right: 20px;
            padding: 8px 15px;
            background-color: #007bff;
            border-radius: 4px;
            text-decoration: none;
        }
        .admin-nav a:hover {
            background-color: #0056b3;
        }
        .product-grid {
            display: grid;
            gap: 20px;
            margin-top: 20px;
        }
        .product-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            gap: 20px;
            align-items: start;
        }
        .product-image {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 5px;
            flex-shrink: 0;
        }
        .product-info {
            flex: 1;
        }
        .product-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
<?php include '../nav.php'; ?>

<div class="admin-nav">
    <a href="index.php">Dashboard</a>
    <a href="users.php">Manage Users</a>
    <a href="products.php">Manage Products</a>
    <a href="categories.php">Manage Categories</a>
    <a href="reports.php">Reports</a>
</div>

<h2>Manage Products</h2>

<?php if ($success_message): ?>
    <div style="color: green; margin: 10px 0; padding: 10px; border: 1px solid green; background-color: #e6ffe6;">
        <?php echo $success_message; ?>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div style="color: red; margin: 10px 0; padding: 10px; border: 1px solid red; background-color: #ffe6e6;">
        <?php echo $error_message; ?>
    </div>
<?php endif; ?>

<div style="margin: 20px 0;">
    <p><strong>Total Products:</strong> <?php echo $products->num_rows; ?></p>
</div>

<?php if ($products->num_rows > 0): ?>
    <div class="product-grid">
        <?php while ($product = $products->fetch_assoc()): ?>
            <div class="product-card">
                <img src="<?php echo htmlspecialchars($product['image']); ?>" 
                     alt="<?php echo htmlspecialchars($product['title']); ?>" 
                     class="product-image"
                     onerror="this.src='https://via.placeholder.com/120x120?text=No+Image'">
                
                <div class="product-info">
                    <h3 style="margin: 0 0 10px 0;"><?php echo htmlspecialchars($product['title']); ?></h3>
                    
                    <div style="margin: 5px 0;">
                        <strong>Seller:</strong> <?php echo htmlspecialchars($product['seller_name']); ?>
                    </div>
                    
                    <div style="margin: 5px 0;">
                        <strong>Category:</strong> <?php echo htmlspecialchars($product['category_name']); ?>
                    </div>
                    
                    <div style="margin: 5px 0;">
                        <strong>Price:</strong> R<?php echo number_format($product['price'], 2); ?>
                    </div>
                    
                    <div style="margin: 5px 0; color: #666;">
                        <strong>Description:</strong> <?php echo htmlspecialchars(substr($product['description'], 0, 150)); ?>
                        <?php if (strlen($product['description']) > 150) echo '...'; ?>
                    </div>
                    
                    <div style="margin: 5px 0; color: #666; font-size: 12px;">
                        <strong>Listed:</strong> <?php echo date('M j, Y g:i A', strtotime($product['created_at'])); ?>
                    </div>
                    
                    <div class="product-actions">
                        <button onclick="viewProduct(<?php echo $product['id']; ?>)" 
                                style="background-color: #007bff; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">
                            View Details
                        </button>
                        
                        <form method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this product?')">
                            <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                            <button type="submit" name="delete_product" 
                                    style="background-color: #dc3545; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">
                                Delete
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- Product Details Modal (Hidden) -->
                <div id="product-details-<?php echo $product['id']; ?>" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 1000;">
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; max-width: 600px; width: 90%; max-height: 80%; overflow-y: auto;">
                        <h3><?php echo htmlspecialchars($product['title']); ?></h3>
                        <img src="<?php echo htmlspecialchars($product['image']); ?>" 
                             alt="<?php echo htmlspecialchars($product['title']); ?>" 
                             style="width: 100%; max-width: 300px; height: auto; margin: 10px 0;"
                             onerror="this.src='https://via.placeholder.com/300x200?text=No+Image'">
                        
                        <p><strong>Seller:</strong> <?php echo htmlspecialchars($product['seller_name']); ?></p>
                        <p><strong>Category:</strong> <?php echo htmlspecialchars($product['category_name']); ?></p>
                        <p><strong>Price:</strong> R<?php echo number_format($product['price'], 2); ?></p>
                        <p><strong>Description:</strong></p>
                        <p><?php echo nl2br(htmlspecialchars($product['description'])); ?></p>
                        <p><strong>Listed:</strong> <?php echo date('M j, Y g:i A', strtotime($product['created_at'])); ?></p>
                        
                        <button onclick="closeProductDetails(<?php echo $product['id']; ?>)" 
                                style="background-color: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-top: 20px;">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        <?php endwhile; ?>
    </div>
<?php else: ?>
    <p>No products found.</p>
<?php endif; ?>

<script>
function viewProduct(productId) {
    document.getElementById('product-details-' + productId).style.display = 'block';
}

function closeProductDetails(productId) {
    document.getElementById('product-details-' + productId).style.display = 'none';
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    if (event.target.style.position === 'fixed') {
        event.target.style.display = 'none';
    }
});
</script>

<?php include '../footer.php'; ?>
</body>
</html>
