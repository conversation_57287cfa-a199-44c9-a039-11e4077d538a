<?php
session_start();
include '../config.php';

if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
    die("Access denied. Admins only.");
}

// Ensure categories table exists (compatible with existing database)
$table_result = $conn->query("CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT
)");

// Update products table to allow NULL category_id if needed
$products_update = $conn->query("ALTER TABLE products MODIFY COLUMN category_id INT NULL");

// Check if tables exist
$categories_exist = $conn->query("SHOW TABLES LIKE 'categories'");
$products_exist = $conn->query("SHOW TABLES LIKE 'products'");

$success_message = "";
$error_message = "";

// Handle category actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['create_category'])) {
        $name = trim($_POST['name']);
        $description = trim($_POST['description']);

        if (empty($name)) {
            $error_message = "Category name is required.";
        } else {
            // First ensure the table exists
            $table_check = $conn->query("SHOW TABLES LIKE 'categories'");
            if ($table_check->num_rows == 0) {
                $create_table = $conn->query("CREATE TABLE categories (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(50) NOT NULL,
                    description TEXT
                )");
                if (!$create_table) {
                    $error_message = "Failed to create categories table: " . $conn->error;
                    return;
                }
            }

            $stmt = $conn->prepare("INSERT INTO categories (name, description) VALUES (?, ?)");
            if ($stmt) {
                $stmt->bind_param("ss", $name, $description);

                if ($stmt->execute()) {
                    $success_message = "Category created successfully!";
                } else {
                    $error_message = "Failed to create category: " . $stmt->error;
                }
                $stmt->close();
            } else {
                $error_message = "Database prepare error: " . $conn->error;
            }
        }
    }
    
    if (isset($_POST['delete_category'])) {
        $category_id = $_POST['category_id'];
        
        // Check if category has products
        $check = $conn->prepare("SELECT COUNT(*) as count FROM products WHERE category_id = ?");
        $check->bind_param("i", $category_id);
        $check->execute();
        $product_count = $check->get_result()->fetch_assoc()['count'];
        
        if ($product_count > 0) {
            $error_message = "Cannot delete category. It has $product_count products assigned to it.";
        } else {
            $stmt = $conn->prepare("DELETE FROM categories WHERE id = ?");
            $stmt->bind_param("i", $category_id);
            if ($stmt->execute()) {
                $success_message = "Category deleted successfully!";
            } else {
                $error_message = "Failed to delete category.";
            }
        }
    }
    
    if (isset($_POST['update_category'])) {
        $category_id = $_POST['category_id'];
        $name = $_POST['name'];
        $description = $_POST['description'];
        
        $stmt = $conn->prepare("UPDATE categories SET name = ?, description = ? WHERE id = ?");
        $stmt->bind_param("ssi", $name, $description, $category_id);
        
        if ($stmt->execute()) {
            $success_message = "Category updated successfully!";
        } else {
            $error_message = "Failed to update category.";
        }
    }
}

// Get all categories with product counts
$categories = $conn->query("SELECT c.*, COUNT(p.id) as product_count 
                           FROM categories c 
                           LEFT JOIN products p ON c.id = p.category_id 
                           GROUP BY c.id 
                           ORDER BY c.name");
?>
<!DOCTYPE html>
<html>
<head>
    <title>Manage Categories - Admin Panel</title>
    <link rel="stylesheet" href="../style.css">
    <style>
        .admin-nav {
            background-color: #343a40;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .admin-nav a {
            color: white;
            margin-right: 20px;
            padding: 8px 15px;
            background-color: #007bff;
            border-radius: 4px;
            text-decoration: none;
        }
        .admin-nav a:hover {
            background-color: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>
<body>
<?php include '../nav.php'; ?>

<div class="admin-nav">
    <a href="index.php">Dashboard</a>
    <a href="users.php">Manage Users</a>
    <a href="products.php">Manage Products</a>
    <a href="categories.php">Manage Categories</a>
    <a href="reports.php">Reports</a>
</div>

<!-- Database Status -->
<div style="background: #f8f9fa; padding: 15px; margin: 20px 0; border-radius: 5px; border-left: 4px solid #007bff;">
    <h4 style="margin: 0 0 10px 0; color: #007bff;">Database Status</h4>
    <p style="margin: 5px 0;">Categories table: <?php echo ($categories_exist && $categories_exist->num_rows > 0) ? '✅ Exists' : '❌ Missing'; ?></p>
    <p style="margin: 5px 0;">Products table: <?php echo ($products_exist && $products_exist->num_rows > 0) ? '✅ Exists' : '❌ Missing'; ?></p>
    <p style="margin: 5px 0;">Connection: <?php echo $conn->ping() ? '✅ Connected' : '❌ Disconnected'; ?></p>
</div>

<h2>Manage Categories</h2>

<?php if ($success_message): ?>
    <div style="color: green; margin: 10px 0; padding: 10px; border: 1px solid green; background-color: #e6ffe6;">
        <?php echo $success_message; ?>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div style="color: red; margin: 10px 0; padding: 10px; border: 1px solid red; background-color: #ffe6e6;">
        <?php echo $error_message; ?>
    </div>
<?php endif; ?>

<!-- Create Category Form -->
<div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
    <h3>Create New Category</h3>
    <form method="post" style="display: grid; grid-template-columns: 1fr 2fr auto; gap: 10px; align-items: end;">
        <input type="text" name="name" placeholder="Category Name" required>
        <input type="text" name="description" placeholder="Description">
        <button type="submit" name="create_category" style="background-color: #28a745;">Create Category</button>
    </form>
</div>

<!-- Categories Table -->
<table>
    <thead>
        <tr>
            <th>ID</th>
            <th>Name</th>
            <th>Description</th>
            <th>Products</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        <?php while ($category = $categories->fetch_assoc()): ?>
            <tr>
                <td><?php echo $category['id']; ?></td>
                <td><?php echo htmlspecialchars($category['name']); ?></td>
                <td><?php echo htmlspecialchars($category['description']); ?></td>
                <td>
                    <span style="background-color: #007bff; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px;">
                        <?php echo $category['product_count']; ?> products
                    </span>
                </td>
                <td>
                    <button onclick="editCategory(<?php echo $category['id']; ?>)" style="background-color: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-right: 5px;">Edit</button>
                    
                    <form method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this category?')">
                        <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">
                        <button type="submit" name="delete_category" style="background-color: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">Delete</button>
                    </form>
                    
                    <!-- Edit Form (Hidden) -->
                    <div id="edit-form-<?php echo $category['id']; ?>" style="display: none; margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
                        <form method="post" style="display: grid; grid-template-columns: 1fr 2fr auto; gap: 10px; align-items: end;">
                            <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">
                            <input type="text" name="name" value="<?php echo htmlspecialchars($category['name']); ?>" required>
                            <input type="text" name="description" value="<?php echo htmlspecialchars($category['description']); ?>">
                            <div>
                                <button type="submit" name="update_category" style="background-color: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-right: 5px;">Save</button>
                                <button type="button" onclick="cancelEdit(<?php echo $category['id']; ?>)" style="background-color: #6c757d; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">Cancel</button>
                            </div>
                        </form>
                    </div>
                </td>
            </tr>
        <?php endwhile; ?>
    </tbody>
</table>

<script>
function editCategory(categoryId) {
    document.getElementById('edit-form-' + categoryId).style.display = 'block';
}

function cancelEdit(categoryId) {
    document.getElementById('edit-form-' + categoryId).style.display = 'none';
}
</script>

<?php include '../footer.php'; ?>
</body>
</html>
