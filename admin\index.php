<?php
session_start();
include '../config.php';

if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
    die("Access denied. Admins only.");
}

// Get statistics
$user_count = $conn->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
$product_count = $conn->query("SELECT COUNT(*) as count FROM products")->fetch_assoc()['count'];
$order_count = $conn->query("SELECT COUNT(*) as count FROM orders")->fetch_assoc()['count'];
$message_count = $conn->query("SELECT COUNT(*) as count FROM messages")->fetch_assoc()['count'];

// Get recent activity
$recent_users = $conn->query("SELECT username, email, role, created_at FROM users ORDER BY created_at DESC LIMIT 5");
$recent_products = $conn->query("SELECT p.title, u.username, p.created_at FROM products p JOIN users u ON p.user_id = u.id ORDER BY p.created_at DESC LIMIT 5");
$recent_orders = $conn->query("SELECT o.id, u.username, o.total, o.created_at FROM orders o JOIN users u ON o.user_id = u.id ORDER BY o.created_at DESC LIMIT 5");
?>
<!DOCTYPE html>
<html>
<head>
    <title>Admin Panel - MarketX</title>
    <link rel="stylesheet" href="../style.css">
    <style>
        .admin-nav {
            background-color: #343a40;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .admin-nav a {
            color: white;
            margin-right: 20px;
            padding: 8px 15px;
            background-color: #007bff;
            border-radius: 4px;
            text-decoration: none;
        }
        .admin-nav a:hover {
            background-color: #0056b3;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
<?php include '../nav.php'; ?>

<div class="admin-nav">
    <a href="index.php">Dashboard</a>
    <a href="users.php">Manage Users</a>
    <a href="products.php">Manage Products</a>
    <a href="categories.php">Manage Categories</a>
    <a href="reports.php">Reports</a>
</div>

<h2>Admin Dashboard</h2>

<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-number"><?php echo $user_count; ?></div>
        <div>Total Users</div>
    </div>
    <div class="stat-card">
        <div class="stat-number"><?php echo $product_count; ?></div>
        <div>Total Products</div>
    </div>
    <div class="stat-card">
        <div class="stat-number"><?php echo $order_count; ?></div>
        <div>Total Orders</div>
    </div>
    <div class="stat-card">
        <div class="stat-number"><?php echo $message_count; ?></div>
        <div>Total Messages</div>
    </div>
</div>

<div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-top: 30px;">
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <h3>Recent Users</h3>
        <?php while ($user = $recent_users->fetch_assoc()): ?>
            <div style="border-bottom: 1px solid #eee; padding: 10px 0;">
                <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                <br><small><?php echo htmlspecialchars($user['email']); ?> (<?php echo $user['role']; ?>)</small>
                <br><small><?php echo date('M j, Y', strtotime($user['created_at'])); ?></small>
            </div>
        <?php endwhile; ?>
    </div>

    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <h3>Recent Products</h3>
        <?php while ($product = $recent_products->fetch_assoc()): ?>
            <div style="border-bottom: 1px solid #eee; padding: 10px 0;">
                <strong><?php echo htmlspecialchars($product['title']); ?></strong>
                <br><small>by <?php echo htmlspecialchars($product['username']); ?></small>
                <br><small><?php echo date('M j, Y', strtotime($product['created_at'])); ?></small>
            </div>
        <?php endwhile; ?>
    </div>

    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <h3>Recent Orders</h3>
        <?php while ($order = $recent_orders->fetch_assoc()): ?>
            <div style="border-bottom: 1px solid #eee; padding: 10px 0;">
                <strong>Order #<?php echo $order['id']; ?></strong>
                <br><small><?php echo htmlspecialchars($order['username']); ?> - R<?php echo number_format($order['total'], 2); ?></small>
                <br><small><?php echo date('M j, Y', strtotime($order['created_at'])); ?></small>
            </div>
        <?php endwhile; ?>
    </div>
</div>

<?php include '../footer.php'; ?>
</body>
</html>
