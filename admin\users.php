<?php
session_start();
include '../config.php';

if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
    die("Access denied. Admins only.");
}

$success_message = "";
$error_message = "";

// Handle user actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['create_user'])) {
        $username = $_POST['username'];
        $email = $_POST['email'];
        $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
        $role = $_POST['role'];
        
        $stmt = $conn->prepare("INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, ?)");
        $stmt->bind_param("ssss", $username, $email, $password, $role);
        
        if ($stmt->execute()) {
            $success_message = "User created successfully!";
        } else {
            $error_message = "Failed to create user.";
        }
    }
    
    if (isset($_POST['delete_user'])) {
        $user_id = $_POST['user_id'];
        if ($user_id != $_SESSION['user']['id']) { // Don't allow admin to delete themselves
            $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
            $stmt->bind_param("i", $user_id);
            if ($stmt->execute()) {
                $success_message = "User deleted successfully!";
            } else {
                $error_message = "Failed to delete user.";
            }
        } else {
            $error_message = "You cannot delete your own account.";
        }
    }
    
    if (isset($_POST['update_user'])) {
        $user_id = $_POST['user_id'];
        $username = $_POST['username'];
        $email = $_POST['email'];
        $role = $_POST['role'];
        
        $stmt = $conn->prepare("UPDATE users SET username = ?, email = ?, role = ? WHERE id = ?");
        $stmt->bind_param("sssi", $username, $email, $role, $user_id);
        
        if ($stmt->execute()) {
            $success_message = "User updated successfully!";
        } else {
            $error_message = "Failed to update user.";
        }
    }
}

// Get all users
$users = $conn->query("SELECT * FROM users ORDER BY created_at DESC");
?>
<!DOCTYPE html>
<html>
<head>
    <title>Manage Users - Admin Panel</title>
    <link rel="stylesheet" href="../style.css">
    <style>
        .admin-nav {
            background-color: #343a40;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .admin-nav a {
            color: white;
            margin-right: 20px;
            padding: 8px 15px;
            background-color: #007bff;
            border-radius: 4px;
            text-decoration: none;
        }
        .admin-nav a:hover {
            background-color: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>
<body>
<?php include '../nav.php'; ?>

<div class="admin-nav">
    <a href="index.php">Dashboard</a>
    <a href="users.php">Manage Users</a>
    <a href="products.php">Manage Products</a>
    <a href="categories.php">Manage Categories</a>
    <a href="reports.php">Reports</a>
</div>

<h2>Manage Users</h2>

<?php if ($success_message): ?>
    <div style="color: green; margin: 10px 0; padding: 10px; border: 1px solid green; background-color: #e6ffe6;">
        <?php echo $success_message; ?>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div style="color: red; margin: 10px 0; padding: 10px; border: 1px solid red; background-color: #ffe6e6;">
        <?php echo $error_message; ?>
    </div>
<?php endif; ?>

<!-- Create User Form -->
<div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
    <h3>Create New User</h3>
    <form method="post" style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr auto; gap: 10px; align-items: end;">
        <input type="text" name="username" placeholder="Username" required>
        <input type="email" name="email" placeholder="Email" required>
        <input type="password" name="password" placeholder="Password" required>
        <select name="role" required>
            <option value="buyer">Buyer</option>
            <option value="seller">Seller</option>
            <option value="admin">Admin</option>
        </select>
        <button type="submit" name="create_user" style="background-color: #28a745;">Create User</button>
    </form>
</div>

<!-- Users Table -->
<table>
    <thead>
        <tr>
            <th>ID</th>
            <th>Username</th>
            <th>Email</th>
            <th>Role</th>
            <th>Created</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        <?php while ($user = $users->fetch_assoc()): ?>
            <tr>
                <td><?php echo $user['id']; ?></td>
                <td><?php echo htmlspecialchars($user['username']); ?></td>
                <td><?php echo htmlspecialchars($user['email']); ?></td>
                <td>
                    <span style="background-color: <?php echo $user['role'] === 'admin' ? '#dc3545' : ($user['role'] === 'seller' ? '#007bff' : '#28a745'); ?>; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px;">
                        <?php echo ucfirst($user['role']); ?>
                    </span>
                </td>
                <td><?php echo date('M j, Y', strtotime($user['created_at'])); ?></td>
                <td>
                    <button onclick="editUser(<?php echo $user['id']; ?>)" style="background-color: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-right: 5px;">Edit</button>
                    
                    <?php if ($user['id'] != $_SESSION['user']['id']): ?>
                        <form method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this user?')">
                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                            <button type="submit" name="delete_user" style="background-color: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">Delete</button>
                        </form>
                    <?php endif; ?>
                    
                    <!-- Edit Form (Hidden) -->
                    <div id="edit-form-<?php echo $user['id']; ?>" style="display: none; margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
                        <form method="post" style="display: grid; grid-template-columns: 1fr 1fr 1fr auto; gap: 10px; align-items: end;">
                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                            <input type="text" name="username" value="<?php echo htmlspecialchars($user['username']); ?>" required>
                            <input type="email" name="email" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                            <select name="role" required>
                                <option value="buyer" <?php echo $user['role'] === 'buyer' ? 'selected' : ''; ?>>Buyer</option>
                                <option value="seller" <?php echo $user['role'] === 'seller' ? 'selected' : ''; ?>>Seller</option>
                                <option value="admin" <?php echo $user['role'] === 'admin' ? 'selected' : ''; ?>>Admin</option>
                            </select>
                            <div>
                                <button type="submit" name="update_user" style="background-color: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-right: 5px;">Save</button>
                                <button type="button" onclick="cancelEdit(<?php echo $user['id']; ?>)" style="background-color: #6c757d; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">Cancel</button>
                            </div>
                        </form>
                    </div>
                </td>
            </tr>
        <?php endwhile; ?>
    </tbody>
</table>

<script>
function editUser(userId) {
    document.getElementById('edit-form-' + userId).style.display = 'block';
}

function cancelEdit(userId) {
    document.getElementById('edit-form-' + userId).style.display = 'none';
}
</script>

<?php include '../footer.php'; ?>
</body>
</html>
<?php
session_start();
include '../config.php';

if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
    die("Access denied. Admins only.");
}

$success_message = "";
$error_message = "";

// Handle user actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['create_user'])) {
        $username = $_POST['username'];
        $email = $_POST['email'];
        $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
        $role = $_POST['role'];
        
        $stmt = $conn->prepare("INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, ?)");
        $stmt->bind_param("ssss", $username, $email, $password, $role);
        
        if ($stmt->execute()) {
            $success_message = "User created successfully!";
        } else {
            $error_message = "Failed to create user.";
        }
    }
    
    if (isset($_POST['delete_user'])) {
        $user_id = $_POST['user_id'];
        if ($user_id != $_SESSION['user']['id']) { // Don't allow admin to delete themselves
            $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
            $stmt->bind_param("i", $user_id);
            if ($stmt->execute()) {
                $success_message = "User deleted successfully!";
            } else {
                $error_message = "Failed to delete user.";
            }
        } else {
            $error_message = "You cannot delete your own account.";
        }
    }
    
    if (isset($_POST['update_user'])) {
        $user_id = $_POST['user_id'];
        $username = $_POST['username'];
        $email = $_POST['email'];
        $role = $_POST['role'];
        
        $stmt = $conn->prepare("UPDATE users SET username = ?, email = ?, role = ? WHERE id = ?");
        $stmt->bind_param("sssi", $username, $email, $role, $user_id);
        
        if ($stmt->execute()) {
            $success_message = "User updated successfully!";
        } else {
            $error_message = "Failed to update user.";
        }
    }
}

// Get all users
$users = $conn->query("SELECT * FROM users ORDER BY created_at DESC");
?>
<!DOCTYPE html>
<html>
<head>
    <title>Manage Users - Admin Panel</title>
    <link rel="stylesheet" href="../style.css">
    <style>
        .admin-nav {
            background-color: #343a40;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .admin-nav a {
            color: white;
            margin-right: 20px;
            padding: 8px 15px;
            background-color: #007bff;
            border-radius: 4px;
            text-decoration: none;
        }
        .admin-nav a:hover {
            background-color: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>
<body>
<?php include '../nav.php'; ?>

<div class="admin-nav">
    <a href="index.php">Dashboard</a>
    <a href="users.php">Manage Users</a>
    <a href="products.php">Manage Products</a>
    <a href="categories.php">Manage Categories</a>
    <a href="reports.php">Reports</a>
</div>

<h2>Manage Users</h2>

<?php if ($success_message): ?>
    <div style="color: green; margin: 10px 0; padding: 10px; border: 1px solid green; background-color: #e6ffe6;">
        <?php echo $success_message; ?>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div style="color: red; margin: 10px 0; padding: 10px; border: 1px solid red; background-color: #ffe6e6;">
        <?php echo $error_message; ?>
    </div>
<?php endif; ?>

<!-- Create User Form -->
<div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
    <h3>Create New User</h3>
    <form method="post" style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr auto; gap: 10px; align-items: end;">
        <input type="text" name="username" placeholder="Username" required>
        <input type="email" name="email" placeholder="Email" required>
        <input type="password" name="password" placeholder="Password" required>
        <select name="role" required>
            <option value="buyer">Buyer</option>
            <option value="seller">Seller</option>
            <option value="admin">Admin</option>
        </select>
        <button type="submit" name="create_user" style="background-color: #28a745;">Create User</button>
    </form>
</div>

<!-- Users Table -->
<table>
    <thead>
        <tr>
            <th>ID</th>
            <th>Username</th>
            <th>Email</th>
            <th>Role</th>
            <th>Created</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        <?php while ($user = $users->fetch_assoc()): ?>
            <tr>
                <td><?php echo $user['id']; ?></td>
                <td><?php echo htmlspecialchars($user['username']); ?></td>
                <td><?php echo htmlspecialchars($user['email']); ?></td>
                <td>
                    <span style="background-color: <?php echo $user['role'] === 'admin' ? '#dc3545' : ($user['role'] === 'seller' ? '#007bff' : '#28a745'); ?>; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px;">
                        <?php echo ucfirst($user['role']); ?>
                    </span>
                </td>
                <td><?php echo date('M j, Y', strtotime($user['created_at'])); ?></td>
                <td>
                    <button onclick="editUser(<?php echo $user['id']; ?>)" style="background-color: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-right: 5px;">Edit</button>
                    
                    <?php if ($user['id'] != $_SESSION['user']['id']): ?>
                        <form method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this user?')">
                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                            <button type="submit" name="delete_user" style="background-color: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">Delete</button>
                        </form>
                    <?php endif; ?>
                    
                    <!-- Edit Form (Hidden) -->
                    <div id="edit-form-<?php echo $user['id']; ?>" style="display: none; margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
                        <form method="post" style="display: grid; grid-template-columns: 1fr 1fr 1fr auto; gap: 10px; align-items: end;">
                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                            <input type="text" name="username" value="<?php echo htmlspecialchars($user['username']); ?>" required>
                            <input type="email" name="email" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                            <select name="role" required>
                                <option value="buyer" <?php echo $user['role'] === 'buyer' ? 'selected' : ''; ?>>Buyer</option>
                                <option value="seller" <?php echo $user['role'] === 'seller' ? 'selected' : ''; ?>>Seller</option>
                                <option value="admin" <?php echo $user['role'] === 'admin' ? 'selected' : ''; ?>>Admin</option>
                            </select>
                            <div>
                                <button type="submit" name="update_user" style="background-color: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-right: 5px;">Save</button>
                                <button type="button" onclick="cancelEdit(<?php echo $user['id']; ?>)" style="background-color: #6c757d; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">Cancel</button>
                            </div>
                        </form>
                    </div>
                </td>
            </tr>
        <?php endwhile; ?>
    </tbody>
</table>

<script>
function editUser(userId) {
    document.getElementById('edit-form-' + userId).style.display = 'block';
}

function cancelEdit(userId) {
    document.getElementById('edit-form-' + userId).style.display = 'none';
}
</script>

<?php include '../footer.php'; ?>
</body>
</html>
