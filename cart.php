<?php
include 'config.php';
session_start();
if (!isset($_SESSION['user'])) {
    die("Please <a href='login.php'>login</a> first.");
}

$user_id = $_SESSION['user']['id'];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_to_cart'])) {
    $product_id = $_POST['product_id'];

    // Check if item already exists in cart
    $check_stmt = $conn->prepare("SELECT id, quantity FROM cart WHERE user_id = ? AND product_id = ?");
    $check_stmt->bind_param("ii", $user_id, $product_id);
    $check_stmt->execute();
    $existing = $check_stmt->get_result()->fetch_assoc();

    if ($existing) {
        // Update quantity if item exists
        $new_quantity = $existing['quantity'] + 1;
        $update_stmt = $conn->prepare("UPDATE cart SET quantity = ? WHERE id = ?");
        $update_stmt->bind_param("ii", $new_quantity, $existing['id']);
        $update_stmt->execute();
    } else {
        // Insert new item if it doesn't exist
        $stmt = $conn->prepare("INSERT INTO cart (user_id, product_id, quantity) VALUES (?, ?, 1)");
        $stmt->bind_param("ii", $user_id, $product_id);
        $stmt->execute();
    }
    echo "Added to cart. <a href='cart.php'>View Cart</a>";
    exit();
}

$stmt = $conn->prepare("SELECT cart.id, products.title, products.price, cart.quantity
                        FROM cart
                        JOIN products ON cart.product_id = products.id
                        WHERE cart.user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

$total = 0;
?>
<!DOCTYPE html>
<html>
<head>
    <title>Your Cart</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
<?php include 'nav.php'; ?>

<main>
    <h2 class="center-title">Your Shopping <span class="highlight">Cart</span></h2>

    <div style="max-width: 800px; margin: 0 auto; padding: 0 20px;">
        <?php
        if ($result->num_rows > 0) {
            echo "<div style='margin: 30px 0;'>";
            while ($row = $result->fetch_assoc()) {
                $line = $row['price'] * $row['quantity'];
                $total += $line;
                echo "<div class='form-container' style='margin-bottom: 20px;'>";
                echo "<div style='display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px;'>";
                echo "<div style='flex: 1; min-width: 200px;'>";
                echo "<h3 style='color: #ff6f61; margin: 0 0 5px 0;'>{$row['title']}</h3>";
                echo "<p style='color: #666; margin: 0;'>Unit Price: R" . number_format($row['price'], 2) . "</p>";
                echo "<p style='color: #666; margin: 0;'>Quantity: {$row['quantity']}</p>";
                echo "</div>";
                echo "<div style='text-align: right;'>";
                echo "<p style='font-size: 18px; font-weight: bold; color: #ff6f61; margin: 0;'>R" . number_format($line, 2) . "</p>";
                echo "</div>";
                echo "</div>";
                echo "</div>";
            }
            echo "</div>";

            echo "<div class='form-container' style='text-align: center; background-color: #f8f9fa;'>";
            echo "<h3 style='color: #ff6f61; font-size: 24px; margin-bottom: 20px;'>Order Total: R" . number_format($total, 2) . "</h3>";
            echo "<div style='display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;'>";
            echo "<a href='index.php' class='btn-yellow' style='padding: 12px 25px;'>Continue Shopping</a>";
            echo "<form method='post' action='checkout.php' style='margin: 0; padding: 0; background: none; box-shadow: none;'>";
            echo "<button type='submit' class='submit-btn' style='padding: 12px 25px; margin: 0;'>Proceed to Checkout</button>";
            echo "</form>";
            echo "</div>";
            echo "</div>";
        } else {
            echo "<div class='form-container' style='text-align: center;'>";
            echo "<h3 style='color: #666; margin-bottom: 20px;'>🛒 Your cart is empty</h3>";
            echo "<p style='color: #666; margin-bottom: 30px;'>Looks like you haven't added any items to your cart yet.</p>";
            echo "<a href='index.php' class='btn-yellow' style='padding: 12px 25px;'>Start Shopping</a>";
            echo "</div>";
        }
        ?>
    </div>
<?php include 'footer.php'; ?>
</body>
</html>
