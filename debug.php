<?php
include 'config.php';
session_start();

// Only allow admin access
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
    header('Location: login.php');
    exit();
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug - MarketX</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
<main style="max-width: 1000px; margin: 0 auto; padding: 20px;">
    <h2>MarketX Debug Information</h2>
    
    <div style="background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px;">
        <h3>Database Connection</h3>
        <p><strong>Status:</strong> <?php echo $conn->ping() ? '✅ Connected' : '❌ Disconnected'; ?></p>
        <p><strong>Host:</strong> <?php echo $conn->host_info; ?></p>
        <p><strong>Database:</strong> <?php echo $conn->get_server_info(); ?></p>
    </div>

    <div style="background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px;">
        <h3>Tables Status</h3>
        <?php
        $tables = ['users', 'products', 'categories', 'cart', 'orders'];
        foreach ($tables as $table) {
            $check = $conn->query("SHOW TABLES LIKE '$table'");
            $exists = $check && $check->num_rows > 0;
            echo "<p><strong>$table:</strong> " . ($exists ? '✅ Exists' : '❌ Missing') . "</p>";
            
            if ($exists) {
                $count = $conn->query("SELECT COUNT(*) as count FROM $table");
                if ($count) {
                    $row = $count->fetch_assoc();
                    echo "<p style='margin-left: 20px; color: #666;'>Records: {$row['count']}</p>";
                }
            }
        }
        ?>
    </div>

    <div style="background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px;">
        <h3>Recent Products</h3>
        <?php
        $products = $conn->query("SELECT id, title, price, image, created_at FROM products ORDER BY created_at DESC LIMIT 5");
        if ($products && $products->num_rows > 0) {
            echo "<table style='width: 100%; border-collapse: collapse;'>";
            echo "<tr style='background: #e9ecef;'><th style='padding: 10px; border: 1px solid #ddd;'>ID</th><th style='padding: 10px; border: 1px solid #ddd;'>Title</th><th style='padding: 10px; border: 1px solid #ddd;'>Price</th><th style='padding: 10px; border: 1px solid #ddd;'>Image</th><th style='padding: 10px; border: 1px solid #ddd;'>Created</th></tr>";
            while ($product = $products->fetch_assoc()) {
                echo "<tr>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>{$product['id']}</td>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . htmlspecialchars($product['title']) . "</td>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>R" . number_format($product['price'], 2) . "</td>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . (empty($product['image']) ? 'No image' : htmlspecialchars($product['image'])) . "</td>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . date('M j, Y', strtotime($product['created_at'])) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No products found.</p>";
        }
        ?>
    </div>

    <div style="background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px;">
        <h3>Categories</h3>
        <?php
        $categories = $conn->query("SELECT * FROM categories ORDER BY name");
        if ($categories && $categories->num_rows > 0) {
            echo "<ul>";
            while ($cat = $categories->fetch_assoc()) {
                echo "<li><strong>{$cat['name']}</strong> - " . htmlspecialchars($cat['description']) . "</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No categories found.</p>";
        }
        ?>
    </div>

    <div style="background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px;">
        <h3>File System</h3>
        <p><strong>Uploads directory:</strong> <?php echo is_dir('uploads') ? '✅ Exists' : '❌ Missing'; ?></p>
        <p><strong>Uploads writable:</strong> <?php echo is_writable('uploads') ? '✅ Yes' : '❌ No'; ?></p>
        <?php
        if (is_dir('uploads')) {
            $files = scandir('uploads');
            $files = array_diff($files, array('.', '..', '.htaccess'));
            echo "<p><strong>Files in uploads:</strong> " . (count($files) > 0 ? count($files) . " files" : "Empty") . "</p>";
            if (count($files) > 0) {
                echo "<ul>";
                foreach ($files as $file) {
                    echo "<li>$file</li>";
                }
                echo "</ul>";
            }
        }
        ?>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <a href="admin/index.php" style="padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;">Back to Admin Panel</a>
    </div>
</main>
</body>
</html>
