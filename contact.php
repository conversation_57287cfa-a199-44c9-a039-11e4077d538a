<?php
session_start();
$success_message = "";
$error_message = "";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = htmlspecialchars($_POST['name']);
    $email = htmlspecialchars($_POST['email']);
    $message = htmlspecialchars($_POST['message']);

    // In a real application, you would send an email or save to database
    // For now, we'll just show a success message
    if (!empty($name) && !empty($email) && !empty($message)) {
        $success_message = "Thank you for your message, $name! We'll get back to you soon.";
    } else {
        $error_message = "Please fill in all fields.";
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Contact Us</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
<?php include 'nav.php'; ?>

<main>
    <h2 class="center-title">Contact <span class="highlight">MarketX</span></h2>
    <p style="text-align: center; margin-bottom: 30px; color: #666; max-width: 600px; margin-left: auto; margin-right: auto;">
        Have a question, suggestion, or need help? We'd love to hear from you! Send us a message and we'll get back to you as soon as possible.
    </p>

    <?php if ($success_message): ?>
        <div class="success-message">
            <?php echo $success_message; ?>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="error-message">
            <?php echo $error_message; ?>
        </div>
    <?php endif; ?>

    <div class="form-container">
        <form method="post">
            <label>Your Name</label>
            <input type="text" name="name" placeholder="Enter your full name" required>

            <label>Your Email</label>
            <input type="email" name="email" placeholder="Enter your email address" required>

            <label>Message</label>
            <textarea name="message" placeholder="Tell us how we can help you..." rows="6" required></textarea>

            <button type="submit" class="submit-btn">Send Message</button>
        </form>
    </div>

    <!-- Contact Information Section -->
    <div style="max-width: 800px; margin: 50px auto; padding: 0 20px;">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px; margin-top: 40px;">
            <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); text-align: center;">
                <h3 style="color: #ff6f61; margin-bottom: 15px;">📧 Email Us</h3>
                <p style="color: #666;"><EMAIL></p>
                <p style="color: #666;">We typically respond within 24 hours</p>
            </div>

            <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); text-align: center;">
                <h3 style="color: #ff6f61; margin-bottom: 15px;">💬 Live Chat</h3>
                <p style="color: #666;">Available Monday - Friday</p>
                <p style="color: #666;">9:00 AM - 6:00 PM</p>
            </div>

            <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); text-align: center;">
                <h3 style="color: #ff6f61; margin-bottom: 15px;">❓ FAQ</h3>
                <p style="color: #666;">Check our frequently asked questions</p>
                <a href="#" style="color: #ff6f61; font-weight: bold;">View FAQ</a>
            </div>
        </div>
    </div>
</main>

<?php include 'footer.php'; ?>
</body>
</html>
