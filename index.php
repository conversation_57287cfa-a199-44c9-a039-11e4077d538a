<?php
include 'config.php';
session_start();
?>
<!DOCTYPE html>
<html>
<head>
    <title>MarketX - Home</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
<?php include 'nav.php'; ?>

<!-- Hero Section -->
<section class="hero">
    <h1>Welcome to MarketX</h1>
    <p>Your one-stop marketplace for buying and selling products!</p>
    <?php if (!isset($_SESSION['user'])): ?>
        <a href="register.php" class="btn-yellow">Get Started Today</a>
    <?php else: ?>
        <a href="#trending" class="btn-yellow">Shop Now</a>
    <?php endif; ?>
</section>

<!-- Search Section -->
<section style="background-color: #f8f8f8; padding: 30px 20px; text-align: center;">
    <h2 style="color: #ff6f61; margin-bottom: 20px;">Find What You're Looking For</h2>
    <div class="form-container" style="background: white;">
        <form method="GET" style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
            <input type="text" name="search" placeholder="Search products..." value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>" style="flex: 1; min-width: 200px;">
            <select name="category" style="width: auto;">
                <option value="">All Categories</option>
                <?php
                $cat_result = $conn->query("SELECT * FROM categories");
                if ($cat_result) {
                    while ($cat = $cat_result->fetch_assoc()) {
                        $selected = (isset($_GET['category']) && $_GET['category'] == $cat['id']) ? 'selected' : '';
                        echo "<option value='{$cat['id']}' $selected>{$cat['name']}</option>";
                    }
                }
                ?>
            </select>
            <button type="submit" class="submit-btn" style="width: auto; margin: 0;">Search</button>
            <?php if (isset($_GET['search']) || isset($_GET['category'])): ?>
                <a href="index.php" style="padding: 10px 20px; background-color: #6c757d; color: white; text-decoration: none; border-radius: 5px;">Clear</a>
            <?php endif; ?>
        </form>
    </div>
</section>
<?php
// Build search query
$where_conditions = [];
$params = [];
$types = "";

if (isset($_GET['search']) && !empty($_GET['search'])) {
    $where_conditions[] = "(title LIKE ? OR description LIKE ?)";
    $search_term = "%" . $_GET['search'] . "%";
    $params[] = $search_term;
    $params[] = $search_term;
    $types .= "ss";
}

if (isset($_GET['category']) && !empty($_GET['category'])) {
    $where_conditions[] = "category_id = ?";
    $params[] = $_GET['category'];
    $types .= "i";
}

$sql = "SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id";
if (!empty($where_conditions)) {
    $sql .= " WHERE " . implode(" AND ", $where_conditions);
}
$sql .= " ORDER BY p.created_at DESC";

if (!empty($params)) {
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
} else {
    $result = $conn->query($sql);
}

if (!$result) {
    echo '<p style="color: red; text-align: center;">Error loading products. Please try again later.</p>';
    exit();
}
?>

<!-- Trending Products Section -->
<section class="trending" id="trending">
    <h2>Trending Products</h2>
    <div class="products">
        <?php
        if ($result->num_rows > 0) {
            $count = 0;
            while ($row = $result->fetch_assoc() && $count < 6) { // Limit to 6 products for trending
                echo '<div class="card">';
                echo '<img src="' . htmlspecialchars($row['image']) . '" alt="' . htmlspecialchars($row['title']) . '" onerror="this.src=\'https://via.placeholder.com/250x200?text=No+Image\'">';
                echo '<h3>' . htmlspecialchars($row['title']) . '</h3>';
                echo '<p style="color: #666; font-size: 14px;">' . htmlspecialchars(substr($row['description'], 0, 80)) . (strlen($row['description']) > 80 ? '...' : '') . '</p>';
                echo '<p class="price">R' . number_format($row['price'], 2) . '</p>';

                if (isset($_SESSION['user'])) {
                    if ($_SESSION['user']['id'] != $row['user_id']) {
                        echo '<div style="display: flex; gap: 5px; margin-top: 10px;">';
                        echo '<form method="post" action="cart.php" style="flex: 1; margin: 0; padding: 0; background: none; box-shadow: none;">';
                        echo '<input type="hidden" name="product_id" value="' . $row['id'] . '">';
                        echo '<button type="submit" name="add_to_cart" class="btn-yellow" style="width: 100%; margin: 0;">Add to Cart</button>';
                        echo '</form>';
                        echo '<a href="message.php?product_id=' . $row['id'] . '" class="btn-yellow" style="flex: 1; text-align: center; padding: 10px 12px;">Message</a>';
                        echo '</div>';
                    } else {
                        echo '<p style="text-align: center; color: #666; font-style: italic;">Your Product</p>';
                    }
                } else {
                    echo '<p style="text-align: center;"><a href="login.php" class="btn-yellow">Login to Purchase</a></p>';
                }
                echo '</div>';
                $count++;
            }
        } else {
            echo '<p style="color: #666;">No products available yet. <a href="register.php" class="btn-yellow">Register as a seller</a> to start listing products!</p>';
        }
        ?>
    </div>
</section>

<!-- Testimonial Section -->
<section class="testimonial">
    <h2>What Our Users Say</h2>
    <p>"MarketX has made buying and selling so easy! The platform is user-friendly and I've had great experiences with both buyers and sellers." - Sarah M.</p>
    <p>"As a seller, I love how simple it is to list my products. The messaging system helps me connect with buyers easily." - John D.</p>
</section>

<!-- Newsletter Section -->
<section class="newsletter">
    <h2>Stay Updated</h2>
    <p>Subscribe to our newsletter for the latest products and deals!</p>
    <form style="margin: 0; padding: 0; background: none; box-shadow: none;">
        <input type="email" placeholder="Enter your email address" required>
        <button type="submit">Subscribe</button>
    </form>
</section>

</main>

<?php include 'footer.php'; ?>
</body>
</html>
