<?php
session_start();
include 'config.php';

if (!isset($_SESSION['user'])) {
    header("Location: login.php");
    exit();
}

$user_id = $_SESSION['user']['id'];
$success_message = "";
$error_message = "";

// Get product info if product_id is provided
$product = null;
if (isset($_GET['product_id'])) {
    $product_id = $_GET['product_id'];
    $stmt = $conn->prepare("SELECT p.*, u.username as seller_name FROM products p JOIN users u ON p.user_id = u.id WHERE p.id = ?");
    $stmt->bind_param("i", $product_id);
    $stmt->execute();
    $product = $stmt->get_result()->fetch_assoc();
}

// Handle message sending
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_message'])) {
    $receiver_id = $_POST['receiver_id'];
    $product_id = $_POST['product_id'] ?? null;
    $subject = $_POST['subject'];
    $message = $_POST['message'];
    
    $stmt = $conn->prepare("INSERT INTO messages (sender_id, receiver_id, product_id, subject, message) VALUES (?, ?, ?, ?, ?)");
    $stmt->bind_param("iiiss", $user_id, $receiver_id, $product_id, $subject, $message);
    
    if ($stmt->execute()) {
        $success_message = "Message sent successfully!";
    } else {
        $error_message = "Failed to send message. Please try again.";
    }
}

// Get user's messages
$stmt = $conn->prepare("SELECT m.*, 
                        sender.username as sender_name, 
                        receiver.username as receiver_name,
                        p.title as product_title
                        FROM messages m 
                        JOIN users sender ON m.sender_id = sender.id 
                        JOIN users receiver ON m.receiver_id = receiver.id 
                        LEFT JOIN products p ON m.product_id = p.id 
                        WHERE m.sender_id = ? OR m.receiver_id = ? 
                        ORDER BY m.created_at DESC");
$stmt->bind_param("ii", $user_id, $user_id);
$stmt->execute();
$messages = $stmt->get_result();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Messages - MarketX</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
<?php include 'nav.php'; ?>

<main>
    <h2 class="center-title">Your <span class="highlight">Messages</span></h2>

    <div style="max-width: 900px; margin: 0 auto; padding: 0 20px;">
        <?php if ($success_message): ?>
            <div class="success-message">
                <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="error-message">
                <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <?php if ($product): ?>
        <div class="form-container">
            <h3 style="color: #ff6f61; margin-bottom: 20px;">Send Message About: <?php echo htmlspecialchars($product['title']); ?></h3>
            <p style="color: #666; margin-bottom: 20px;"><strong>Seller:</strong> <?php echo htmlspecialchars($product['seller_name']); ?></p>

            <form method="post">
                <input type="hidden" name="receiver_id" value="<?php echo $product['user_id']; ?>">
                <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">

                <label>Subject</label>
                <input type="text" name="subject" placeholder="Subject" value="Inquiry about <?php echo htmlspecialchars($product['title']); ?>" required>

                <label>Your Message</label>
                <textarea name="message" placeholder="Your message..." rows="5" required></textarea>

                <button type="submit" name="send_message" class="submit-btn">Send Message</button>
            </form>
        </div>
        <?php endif; ?>

        <div class="form-container">
            <h3 style="color: #ff6f61; margin-bottom: 25px;">💬 Your Messages</h3>
            <?php if ($messages->num_rows > 0): ?>
                <div style="max-height: 600px; overflow-y: auto;">
                    <?php while ($msg = $messages->fetch_assoc()): ?>
                        <div style="border: 1px solid #ddd; padding: 20px; margin: 15px 0; border-radius: 8px; <?php echo ($msg['receiver_id'] == $user_id) ? 'background-color: #f8f9fa; border-left: 4px solid #28a745;' : 'background-color: #e3f2fd; border-left: 4px solid #007bff;'; ?>">
                            <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 15px; flex-wrap: wrap; gap: 10px;">
                                <h4 style="color: #ff6f61; margin: 0; flex: 1;"><?php echo htmlspecialchars($msg['subject']); ?></h4>
                                <small style="color: #666; white-space: nowrap;"><?php echo date('M j, Y g:i A', strtotime($msg['created_at'])); ?></small>
                            </div>

                            <div style="margin-bottom: 10px;">
                                <?php if ($msg['sender_id'] == $user_id): ?>
                                    <span style="background-color: #007bff; color: white; padding: 3px 8px; border-radius: 12px; font-size: 12px;">To: <?php echo htmlspecialchars($msg['receiver_name']); ?></span>
                                <?php else: ?>
                                    <span style="background-color: #28a745; color: white; padding: 3px 8px; border-radius: 12px; font-size: 12px;">From: <?php echo htmlspecialchars($msg['sender_name']); ?></span>
                                <?php endif; ?>

                                <?php if ($msg['product_title']): ?>
                                    <span style="background-color: #ffd700; color: black; padding: 3px 8px; border-radius: 12px; font-size: 12px; margin-left: 5px;">📦 <?php echo htmlspecialchars($msg['product_title']); ?></span>
                                <?php endif; ?>
                            </div>

                            <p style="margin: 15px 0; line-height: 1.6; color: #333;"><?php echo nl2br(htmlspecialchars($msg['message'])); ?></p>

                            <?php if ($msg['sender_id'] != $user_id): ?>
                                <a href="message.php?reply_to=<?php echo $msg['sender_id']; ?>&subject=Re: <?php echo urlencode($msg['subject']); ?>"
                                   class="btn-yellow" style="padding: 8px 15px; font-size: 14px;">Reply</a>
                            <?php endif; ?>
                        </div>
                    <?php endwhile; ?>
                </div>
            <?php else: ?>
                <div style="text-align: center; padding: 40px 0;">
                    <div style="font-size: 48px; margin-bottom: 20px;">📭</div>
                    <h4 style="color: #666; margin-bottom: 15px;">No messages yet</h4>
                    <p style="color: #666; margin-bottom: 25px;">When you contact sellers or receive messages, they'll appear here.</p>
                    <a href="index.php" class="btn-yellow">Browse Products</a>
                </div>
            <?php endif; ?>
        </div>

        <?php if (isset($_GET['reply_to'])): ?>
            <?php
            $reply_to = $_GET['reply_to'];
            $stmt = $conn->prepare("SELECT username FROM users WHERE id = ?");
            $stmt->bind_param("i", $reply_to);
            $stmt->execute();
            $reply_user = $stmt->get_result()->fetch_assoc();
            ?>
            <div class="form-container">
                <h3 style="color: #ff6f61; margin-bottom: 20px;">✉️ Reply to <?php echo htmlspecialchars($reply_user['username']); ?></h3>
                <form method="post">
                    <input type="hidden" name="receiver_id" value="<?php echo $reply_to; ?>">

                    <label>Subject</label>
                    <input type="text" name="subject" placeholder="Subject" value="<?php echo isset($_GET['subject']) ? htmlspecialchars($_GET['subject']) : ''; ?>" required>

                    <label>Your Reply</label>
                    <textarea name="message" placeholder="Your reply..." rows="5" required></textarea>

                    <button type="submit" name="send_message" class="submit-btn">Send Reply</button>
                </form>
            </div>
        <?php endif; ?>
    </div>
</main>

<?php include 'footer.php'; ?>
</body>
</html>
