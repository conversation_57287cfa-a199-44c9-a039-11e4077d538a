* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
}

body {
    background-color: #fff;
    color: #333;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #ff6f61;
    margin-bottom: 20px;
}

h1 {
    text-align: center;
    margin: 40px 0 20px;
    font-size: 26px;
}

.center-title {
    text-align: center;
    margin: 40px 0 20px;
    font-size: 26px;
    color: #ff6f61;
}

.highlight {
    color: #ff6f61;
    font-weight: bold;
}

a {
    text-decoration: none;
    color: #ff6f61;
    transition: color 0.3s ease;
}

a:hover {
    color: #e55a4f;
}

.form-container {
    max-width: 600px;
    margin: auto;
    background-color: #fdfdfd;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

form {
    margin: 0;
    padding: 0;
    background: none;
    box-shadow: none;
    max-width: none;
}

form label {
    display: block;
    margin-top: 20px;
    margin-bottom: 6px;
    font-weight: bold;
    color: #333;
}

form input, form textarea, form select {
    width: 100%;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ccc;
    margin: 0;
    font-size: 14px;
    box-sizing: border-box;
}

button, .submit-btn {
    background-color: #ffd700;
    color: black;
    border: none;
    padding: 10px 20px;
    margin-top: 20px;
    cursor: pointer;
    border-radius: 5px;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

button:hover, .submit-btn:hover {
    background-color: #e6c200;
}

.product {
    border: 1px solid #ddd;
    background: #fff;
    padding: 15px;
    margin: 10px;
    width: 250px;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.product img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-radius: 5px;
    margin-bottom: 10px;
}

.product h3 {
    margin: 10px 0;
    color: #ff6f61;
    font-size: 18px;
    font-weight: bold;
}

/* Header and Navigation styles */
header {
    background-color: #ff6f61;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 50px;
    color: white;
    margin-bottom: 0;
}

.logo {
    font-weight: bold;
    font-size: 24px;
    color: #ffd700;
}

nav a {
    margin-left: 25px;
    color: white !important;
    text-decoration: none;
    font-size: 14px;
    transition: opacity 0.3s ease;
}

nav a:hover {
    opacity: 0.8;
}

nav a.active {
    font-weight: bold;
    text-decoration: underline;
}

/* Footer styles */
footer {
    background-color: #ff6f61;
    color: white;
    text-align: center;
    padding: 20px;
    margin-top: 50px;
}

footer a {
    color: white;
    text-decoration: underline;
}

/* Additional button styles */
.product button, .product a {
    background-color: #ffd700 !important;
    color: black !important;
    border: none;
    padding: 8px 12px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.product button:hover, .product a:hover {
    background-color: #e6c200 !important;
}

/* Success and error messages */
.success-message {
    color: green;
    margin: 10px auto;
    padding: 10px;
    border: 1px solid green;
    background-color: #e6ffe6;
    max-width: 600px;
    border-radius: 5px;
    text-align: center;
}

.error-message {
    color: red;
    margin: 10px auto;
    padding: 10px;
    border: 1px solid red;
    background-color: #ffe6e6;
    max-width: 600px;
    border-radius: 5px;
    text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        padding: 15px 20px;
        text-align: center;
    }

    nav {
        margin-top: 15px;
    }

    nav a {
        margin: 0 10px;
        font-size: 12px;
    }

    .product {
        width: 100%;
        margin: 10px 0;
    }

    .form-container {
        margin: 20px;
        padding: 20px;
    }

    .center-title {
        font-size: 22px;
    }
}

/* Blue theme styles for About page */
.blue-header {
    background-color: #00479b;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 50px;
    color: white;
    margin-bottom: 0;
}

.blue-header .logo.light {
    font-weight: bold;
    font-size: 24px;
    color: white;
}

.blue-header nav a {
    margin-left: 25px;
    color: white !important;
    text-decoration: none;
    font-size: 14px;
    transition: opacity 0.3s ease;
}

.blue-header nav a:hover {
    opacity: 0.8;
}

.blue-header nav a.active {
    font-weight: bold;
    text-decoration: underline;
}

.blue-footer {
    background-color: #002c60;
    color: white;
    text-align: center;
    padding: 20px;
    margin-top: 50px;
}

.blue-footer a {
    color: white;
    text-decoration: underline;
    margin: 0 5px;
}

.about-container {
    max-width: 800px;
    margin: 40px auto;
    padding: 0 20px;
}

.about-box {
    background-color: #fff;
    padding: 25px;
    border-radius: 10px;
    margin-bottom: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.about-box h3 {
    color: #00479b;
    margin-bottom: 15px;
    font-size: 20px;
}

.about-box p {
    line-height: 1.6;
    color: #333;
    margin-bottom: 10px;
}

.about-box ul {
    color: #333;
}

.about-box li {
    margin-bottom: 8px;
}

.center-title.blue {
    color: #00479b;
    text-align: center;
    margin-bottom: 30px;
    font-size: 28px;
}

/* Homepage Hero and Sections */
.hero {
    background-color: #666;
    color: white;
    text-align: center;
    padding: 80px 20px;
}

.hero h1 {
    font-size: 36px;
    margin-bottom: 10px;
    color: white;
}

.hero p {
    font-size: 16px;
    margin-bottom: 20px;
    color: white;
}

.btn-yellow {
    background-color: #ffd700;
    border: none;
    padding: 10px 20px;
    font-weight: bold;
    cursor: pointer;
    border-radius: 5px;
    text-decoration: none;
    color: black;
    display: inline-block;
    transition: background-color 0.3s ease;
}

.btn-yellow:hover {
    background-color: #e6c200;
    color: black;
}

.trending {
    background-color: #f8f8f8;
    text-align: center;
    padding: 50px 20px;
}

.trending h2 {
    font-size: 24px;
    color: #ff6f61;
    margin-bottom: 30px;
}

.products {
    display: flex;
    justify-content: center;
    gap: 25px;
    flex-wrap: wrap;
    max-width: 1200px;
    margin: 0 auto;
}

.card {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    width: 250px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 6px;
    margin-bottom: 10px;
}

.card h3 {
    color: #ff6f61;
    margin: 10px 0;
    font-size: 18px;
}

.price {
    color: red;
    font-weight: bold;
    margin: 10px 0;
    font-size: 18px;
}

.testimonial {
    background-color: #223344;
    color: white;
    text-align: center;
    padding: 40px 20px;
}

.testimonial h2 {
    color: white;
    margin-bottom: 20px;
}

.testimonial p {
    margin: 15px auto;
    max-width: 600px;
    font-style: italic;
}

.newsletter {
    background-color: #ffd700;
    text-align: center;
    padding: 30px 20px;
}

.newsletter h2 {
    color: black;
    margin-bottom: 10px;
}

.newsletter p {
    color: black;
    margin-bottom: 20px;
}

.newsletter input[type="email"] {
    padding: 10px;
    width: 250px;
    max-width: 90%;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-right: 10px;
}

.newsletter button {
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    font-weight: bold;
    background-color: white;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.newsletter button:hover {
    background-color: #f0f0f0;
}
