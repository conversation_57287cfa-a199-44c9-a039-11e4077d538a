<?php
session_start();
include 'config.php';

if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'seller') {
    die("Access denied. Sellers only.");
}

$user_id = $_SESSION['user']['id'];
$success_message = "";
$error_message = "";

// Handle product deletion
if (isset($_POST['delete_product'])) {
    $product_id = $_POST['product_id'];
    $stmt = $conn->prepare("DELETE FROM products WHERE id = ? AND user_id = ?");
    $stmt->bind_param("ii", $product_id, $user_id);
    if ($stmt->execute()) {
        $success_message = "Product deleted successfully!";
    } else {
        $error_message = "Failed to delete product.";
    }
}

// Handle product editing
if (isset($_POST['edit_product'])) {
    $product_id = $_POST['product_id'];
    $title = $_POST['title'];
    $description = $_POST['description'];
    $price = $_POST['price'];
    $image = $_POST['image'];
    $category_id = empty($_POST['category_id']) ? NULL : $_POST['category_id'];
    
    $stmt = $conn->prepare("UPDATE products SET title = ?, description = ?, price = ?, image = ?, category_id = ? WHERE id = ? AND user_id = ?");
    $stmt->bind_param("sssssii", $title, $description, $price, $image, $category_id, $product_id, $user_id);
    
    if ($stmt->execute()) {
        $success_message = "Product updated successfully!";
    } else {
        $error_message = "Failed to update product.";
    }
}

// Get seller's products
$stmt = $conn->prepare("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE p.user_id = ? ORDER BY p.created_at DESC");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$products = $stmt->get_result();

// Get categories for dropdown
$categories = $conn->query("SELECT * FROM categories");
?>
<!DOCTYPE html>
<html>
<head>
    <title>My Products - MarketX</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
<?php include 'nav.php'; ?>

<main>
    <h2 class="center-title">My <span class="highlight">Products</span></h2>
    <p style="text-align: center; margin-bottom: 30px; color: #666;">Manage your product listings, edit details, and track your sales.</p>

    <div style="max-width: 1000px; margin: 0 auto; padding: 0 20px;">
        <?php if ($success_message): ?>
            <div class="success-message">
                <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="error-message">
                <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <div style="text-align: center; margin: 30px 0;">
            <a href="sell.php" class="btn-yellow" style="padding: 12px 25px; font-size: 16px;">📦 Add New Product</a>
        </div>

        <?php if ($products->num_rows > 0): ?>
            <div style="display: grid; gap: 25px; margin-top: 30px;">
                <?php while ($product = $products->fetch_assoc()): ?>
                    <div class="form-container">
                        <div style="display: flex; gap: 25px; align-items: start; flex-wrap: wrap;">
                            <img src="<?php echo htmlspecialchars($product['image']); ?>" alt="<?php echo htmlspecialchars($product['title']); ?>"
                                 style="width: 180px; height: 180px; object-fit: cover; border-radius: 8px; flex-shrink: 0;"
                                 onerror="this.src='https://via.placeholder.com/180x180?text=No+Image'">

                            <div style="flex: 1; min-width: 300px;">
                                <h3 style="color: #ff6f61; margin-bottom: 10px; font-size: 20px;"><?php echo htmlspecialchars($product['title']); ?></h3>

                                <div style="margin-bottom: 15px;">
                                    <span style="background-color: #f8f9fa; color: #ff6f61; padding: 4px 12px; border-radius: 15px; font-size: 12px; font-weight: bold;">
                                        📂 <?php echo $product['category_name'] ? htmlspecialchars($product['category_name']) : 'No Category'; ?>
                                    </span>
                                </div>

                                <p style="color: #666; margin-bottom: 15px; line-height: 1.5;"><?php echo htmlspecialchars($product['description']); ?></p>

                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; flex-wrap: wrap; gap: 10px;">
                                    <span style="font-size: 20px; font-weight: bold; color: #ff6f61;">R<?php echo number_format($product['price'], 2); ?></span>
                                    <span style="color: #666; font-size: 14px;">📅 Listed: <?php echo date('M j, Y', strtotime($product['created_at'])); ?></span>
                                </div>

                                <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                                    <button onclick="editProduct(<?php echo $product['id']; ?>)"
                                            class="btn-yellow" style="padding: 10px 20px;">✏️ Edit</button>

                                    <form method="post" style="display: inline; margin: 0; padding: 0; background: none; box-shadow: none;" onsubmit="return confirm('Are you sure you want to delete this product?')">
                                        <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                        <button type="submit" name="delete_product"
                                                style="background-color: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; font-weight: bold;">🗑️ Delete</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                
                        <!-- Edit Form (Hidden by default) -->
                        <div id="edit-form-<?php echo $product['id']; ?>" style="display: none; margin-top: 25px; border-top: 2px solid #ff6f61; padding-top: 25px;">
                            <h4 style="color: #ff6f61; margin-bottom: 20px;">✏️ Edit Product</h4>
                            <form method="post" style="margin: 0; padding: 0; background: none; box-shadow: none;">
                                <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">

                                <label>Product Name</label>
                                <input type="text" name="title" value="<?php echo htmlspecialchars($product['title']); ?>" required>

                                <label>Category (Optional)</label>
                                <select name="category_id">
                                    <option value="">No Category</option>
                                    <?php
                                    $categories->data_seek(0); // Reset pointer
                                    while ($cat = $categories->fetch_assoc()) {
                                        $selected = ($cat['id'] == $product['category_id']) ? 'selected' : '';
                                        echo "<option value='{$cat['id']}' $selected>{$cat['name']}</option>";
                                    }
                                    ?>
                                </select>

                                <label>Description</label>
                                <textarea name="description" rows="4" required><?php echo htmlspecialchars($product['description']); ?></textarea>

                                <label>Price (R)</label>
                                <input type="number" name="price" step="0.01" value="<?php echo $product['price']; ?>" required>

                                <label>Image URL</label>
                                <input type="text" name="image" value="<?php echo htmlspecialchars($product['image']); ?>" required>

                                <div style="display: flex; gap: 10px; margin-top: 20px; flex-wrap: wrap;">
                                    <button type="submit" name="edit_product" class="submit-btn" style="margin: 0;">💾 Save Changes</button>
                                    <button type="button" onclick="cancelEdit(<?php echo $product['id']; ?>)"
                                            style="background-color: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; font-weight: bold;">❌ Cancel</button>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>
        <?php else: ?>
            <div class="form-container" style="text-align: center;">
                <div style="font-size: 48px; margin-bottom: 20px;">📦</div>
                <h3 style="color: #666; margin-bottom: 15px;">No products listed yet</h3>
                <p style="color: #666; margin-bottom: 30px;">You haven't created any product listings. Start selling by adding your first product!</p>
                <a href="sell.php" class="btn-yellow" style="padding: 12px 25px;">Create Your First Listing</a>
            </div>
        <?php endif; ?>
    </div>
</main>

<script>
function editProduct(productId) {
    document.getElementById('edit-form-' + productId).style.display = 'block';
    // Scroll to the edit form
    document.getElementById('edit-form-' + productId).scrollIntoView({ behavior: 'smooth' });
}

function cancelEdit(productId) {
    document.getElementById('edit-form-' + productId).style.display = 'none';
}
</script>

<?php include 'footer.php'; ?>
</body>
</html>
