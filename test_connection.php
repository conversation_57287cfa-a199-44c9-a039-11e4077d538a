<?php
// Simple test file to check database connection
include 'config.php';

echo "<h2>MarketX Database Connection Test</h2>";
echo "<p><strong>Hosting:</strong> InfinityFree</p>";
echo "<p><strong>Database:</strong> if0_39160898_marketx</p>";

// Test database connection
if ($conn->connect_error) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $conn->connect_error . "</p>";
    echo "<p>Please check your database configuration in config.php</p>";
    echo "<p>Make sure the database is properly imported on InfinityFree hosting.</p>";
} else {
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    echo "<p style='color: green;'>✅ Connected to InfinityFree hosting!</p>";
    
    // Test if tables exist
    $tables = ['users', 'categories', 'products', 'cart', 'orders', 'order_items', 'messages'];
    echo "<h3>Table Status:</h3>";
    
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            echo "<p style='color: green;'>✅ Table '$table' exists</p>";
        } else {
            echo "<p style='color: red;'>❌ Table '$table' missing</p>";
        }
    }
    
    // Count records
    echo "<h3>Data Status:</h3>";
    $user_count = $conn->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
    $product_count = $conn->query("SELECT COUNT(*) as count FROM products")->fetch_assoc()['count'];
    
    echo "<p>Users: $user_count</p>";
    echo "<p>Products: $product_count</p>";
    
    if ($user_count == 0) {
        echo "<p style='color: orange;'>⚠️ No users found. You may need to import the sample data from db.sql</p>";
    }
    
    if ($product_count == 0) {
        echo "<p style='color: orange;'>⚠️ No products found. You may need to import the sample data from db.sql</p>";
    }
}

echo "<hr>";
echo "<p><a href='index.php'>Go to MarketX Homepage</a></p>";
echo "<p><em>Note: Delete this file after testing for security.</em></p>";
?>
